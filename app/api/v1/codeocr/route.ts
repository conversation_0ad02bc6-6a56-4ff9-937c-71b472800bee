import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // Get the form data from the request
    const formData = await request.formData();

    // Check if formData contains a file
    const imageFile = formData.get("file");
    if (!imageFile || !(imageFile instanceof File)) {
      return NextResponse.json(
        { error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return NextResponse.json(
        {
          error:
            "Invalid file type. Please upload PNG, JPG, JPEG, or WebP images.",
        },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 5MB limit." },
        { status: 400 }
      );
    }

    // Convert file to base64 for the FastAPI backend
    const arrayBuffer = await imageFile.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");
    const mimeType = imageFile.type;
    const base64DataUrl = `data:${mimeType};base64,${base64}`;

    // Get optional API settings from form data
    const apiKey = formData.get("api_key") as string | null;
    const apiBase = formData.get("api_base") as string | null;
    const modelName = formData.get("model_name") as string | null;

    // Prepare form data for the backend
    const backendFormData = new FormData();
    backendFormData.append("image_source", base64DataUrl);

    if (apiKey) backendFormData.append("api_key", apiKey);
    if (apiBase) backendFormData.append("api_base", apiBase);
    if (modelName) backendFormData.append("model_name", modelName);

    // Forward the request to the FastAPI backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";

    const response = await fetch(`${backendUrl}/api/v1/codeocr`, {
      method: "POST",
      body: backendFormData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.detail || "Backend processing failed" },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in CodeOCR API route:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
