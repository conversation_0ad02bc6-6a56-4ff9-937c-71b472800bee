"""CodeOCR API server for converting code screenshots to editable code."""

import os
from pathlib import Path

import instructor
import logfire
import uvicorn
from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>, Form
from fastapi.middleware.cors import CORSMiddleware
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, field_validator

load_dotenv(dotenv_path=".env.development", override=True)


class ImageInput(BaseModel):
    """Input model for image data supporting multiple image source formats.

    This model validates and structures the incoming image data for processing
    by the code extraction service. The source field supports various formats
    that can be automatically detected by the instructor library.

    Attributes:
        source: Image source that can be a URL, file path, or base64-encoded string.
                The instructor library's Image.autodetect() method automatically
                determines the format and processes accordingly.
    """

    source: str | Path = Field(
        ...,
        description=(
            "Image source supporting URL, file path, or base64-encoded content "
            "for code extraction."
        ),
    )


class CodeOCRResponse(BaseModel):
    """Response model containing detected programming language and extracted code.

    This model structures the output from the code extraction service, providing
    both the extracted code content and the automatically detected or specified
    programming language.

    Attributes:
        language: The detected or specified programming language of the extracted code.
        code: The code extracted from the input image.
        status: The status of the code extraction process.
    """

    language: str = Field(
        ..., description="Programming language of the extracted code."
    )
    code: str = Field(..., description="The code extracted from the image.")
    status: str = Field(
        "completed", description="Status of the code extraction process."
    )

    @field_validator("language")
    @classmethod
    def lowercase_language(cls, v: str) -> str:
        return v.lower()


# Initialize OpenAI client with custom configuration
openai_client = AsyncOpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)

# Create instructor client for structured output generation
client = instructor.from_openai(
    openai_client,
    mode=instructor.Mode.JSON,
)

# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging and instrumentation for monitoring
logfire.configure(pydantic_plugin=logfire.PydanticPlugin(record="all"))
logfire.instrument_openai(openai_client)
logfire.instrument_fastapi(app)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.get("/api/v1/models")
async def get_models():
    """Get available models from the OpenAI API."""
    try:
        models_client = AsyncOpenAI(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_API_BASE"),
        )
        models_response = await models_client.models.list()
        models = [model.id for model in models_response.data]
        return {"models": models}
    except Exception as e:
        return {"models": [], "error": str(e)}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(
    image_source: str = Form(..., description="Base64 encoded image data"),
    api_key: str = Form(None, description="Optional API key"),
    api_base: str = Form(None, description="Optional API base URL"),
    model_name: str = Form(None, description="Optional model name"),
) -> CodeOCRResponse:
    """Extract code from an image using a vision model.

    This endpoint processes images containing code from base64-encoded data
    and uses LLM's vision capabilities to extract the code content and
    automatically detect the programming language. The service leverages
    structured output generation to ensure consistent response formatting.

    Args:
        image_source: Base64 encoded image data.
        api_key: Optional API key for custom OpenAI configuration.
        api_base: Optional API base URL for custom OpenAI configuration.
        model_name: Optional model name for custom OpenAI configuration.

    Returns:
        CodeOCRResponse containing the extracted code and detected programming language.

    Raises:
        HTTPException: If the image processing fails or the model cannot extract code.
        ValidationError: If the input image format is invalid.
    """
    # Use custom configuration if provided, otherwise use default
    used_api_key = api_key or os.getenv("OPENAI_API_KEY")
    used_api_base = api_base or os.getenv("OPENAI_API_BASE")
    used_model_name = model_name or os.getenv("MODEL_NAME")

    # Create custom client if different from default
    if (api_key and api_key != os.getenv("OPENAI_API_KEY")) or (
        api_base and api_base != os.getenv("OPENAI_API_BASE")
    ):
        custom_client = AsyncOpenAI(
            api_key=used_api_key,
            base_url=used_api_base,
        )
        custom_instructor_client = instructor.from_openai(
            custom_client,
            mode=instructor.Mode.JSON,
        )
        used_client = custom_instructor_client
    else:
        used_client = client

    response = await used_client.chat.completions.create(
        model=used_model_name,
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image",
                    Image.autodetect(image_source),
                ],
            },
        ],
    )
    return response


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
