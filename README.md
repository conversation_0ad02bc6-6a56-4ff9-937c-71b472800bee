# CodeOCR - AI-Powered Code Screenshot to Text Converter

Convert code screenshots to copyable text using AI-powered OCR technology. This application uses OpenAI's vision models to extract code from images with high accuracy and automatic language detection.

## Features

- 🖼️ **Multiple Upload Methods**: Drag & drop, click to browse, or paste from clipboard (Ctrl+V/Cmd+V)
- 🤖 **AI-Powered OCR**: Uses OpenAI's GPT-4 Vision model for accurate code extraction
- 🎨 **Syntax Highlighting**: Automatic language detection and syntax highlighting
- 📋 **One-Click Copy**: Copy extracted code to clipboard with a single click
- 🌙 **Dark Mode Support**: Responsive design with light/dark theme support
- 📱 **Mobile Friendly**: Works on desktop and mobile devices
- ⚡ **Fast Processing**: Quick image processing and code extraction

## Tech Stack

### Frontend

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible UI components
- **React Syntax Highlighter** - Code syntax highlighting
- **Sonner** - Toast notifications

### Backend

- **FastAPI** - Modern Python web framework
- **OpenAI API** - GPT-4 Vision for OCR processing
- **Instructor** - Structured output from LLMs
- **Pydantic** - Data validation and serialization
- **Uvicorn** - ASGI server

## Prerequisites

- Node.js 18+ and npm/yarn/pnpm
- Python 3.8+
- OpenAI API key

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd codeocr
```

### 2. Install Frontend Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Install Backend Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 4. Environment Setup

#### Backend Environment

Create a `.env` file in the `backend` directory:

```bash
cd backend
cp .env.example .env
```

Edit the `.env` file and add your OpenAI API key:

```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
MODEL_NAME=gpt-4o
HOST=127.0.0.1
PORT=8000
DEBUG=True
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

#### Frontend Environment (Optional)

Create a `.env.local` file in the root directory if you want to customize the backend URL:

```env
BACKEND_URL=http://localhost:8000
```

## Running the Application

### Option 1: Run Both Frontend and Backend Together

```bash
npm run dev:full
```

This will start both the Next.js frontend (port 3000) and FastAPI backend (port 8000) concurrently.

### Option 2: Run Separately

#### Start the Backend

```bash
cd backend
python main.py
```

#### Start the Frontend (in a new terminal)

```bash
npm run dev
```

## Usage

1. **Open the Application**: Navigate to `http://localhost:3000`

2. **Upload an Image**:

   - Drag and drop a code screenshot
   - Click "Choose File" to browse
   - Paste from clipboard (Ctrl+V / Cmd+V)

3. **Extract Code**: Click the "Extract Code" button

4. **Copy Result**: Use the "Copy Code" button to copy the extracted code

## Supported Image Formats

- PNG
- JPG/JPEG
- WebP
- Maximum file size: 5MB

## API Endpoints

### Frontend API

- `POST /api/ocr` - Upload image and get extracted code

### Backend API

- `GET /` - Health check
- `POST /api/v1/codeocr` - Endpoint for converting screenshots to code

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── [locale]/          # Internationalized pages
│   └── api/               # API routes
├── backend/               # FastAPI backend
│   ├── main.py           # FastAPI application
│   ├── models.py         # Pydantic models
│   └── requirements.txt  # Python dependencies
├── components/           # React components
│   ├── CodeOCRApp.tsx   # Main application component
│   └── ImageUpload.tsx  # Image upload component
└── package.json         # Node.js dependencies
```

## Development

### Adding New Features

1. **Frontend**: Add components in the `components/` directory
2. **Backend**: Extend the FastAPI application in `backend/main.py`
3. **API**: Add new routes in `app/api/`

### Customizing OCR Behavior

Edit the prompt in `backend/main.py` to customize how the AI extracts code:

```python
"Extract the code from this image. Identify the programming language and return the exact code as it appears in the image. Preserve formatting, indentation, and structure."
```

## Troubleshooting

### Common Issues

1. **OpenAI API Key Error**: Make sure your API key is correctly set in `backend/.env`

2. **CORS Issues**: Ensure the frontend URL is included in `ALLOWED_ORIGINS` in the backend `.env`

3. **File Upload Issues**: Check that the file size is under 5MB and the format is supported

4. **Backend Connection Error**: Verify the backend is running on port 8000 and accessible

### Logs

- **Frontend**: Check the browser console for errors
- **Backend**: Check the terminal running the FastAPI server

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Acknowledgments

- OpenAI for the GPT-4 Vision API
- The FastAPI and Next.js communities
- All contributors and users of this project
