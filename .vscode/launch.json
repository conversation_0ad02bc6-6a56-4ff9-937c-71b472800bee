{"version": "0.2.0", "configurations": [{"name": "Debug Next.js Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "serverReadyAction": {"pattern": "- Local:\\s+([^\\s]+)", "uriFormat": "%s", "action": "openExternally"}}, {"name": "Debug Python FastAPI", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["backend.main:app", "--reload", "--port", "8000"], "jinja": true, "justMyCode": false, "cwd": "${workspaceFolder}", "console": "integratedTerminal"}], "compounds": [{"name": "Debug Full Stack (Next.js + Python)", "configurations": ["Debug Next.js Server", "Debug Python FastAPI"], "stopAll": true, "presentation": {"hidden": false, "group": "", "order": 1}}]}